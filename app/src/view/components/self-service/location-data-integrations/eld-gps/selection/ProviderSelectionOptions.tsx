import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { SearchIcon, XIcon, PhoneIcon } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";

import EldVoiceOnboardingModal from "../voice-onboarding/EldVoiceOnboardingModal";

import styles from "./ProviderSelectionOptions.module.scss";

interface ProviderSelectionOptionsProps {
  query: string;
  setQuery: (query: string) => void;
  onBrowseProviders: () => void;
  className?: string;
}

const ProviderSelectionOptions: React.FC<ProviderSelectionOptionsProps> = ({
  query,
  setQuery,
  onBrowseProviders,
  className,
}) => {
  const { t } = useTranslation();
  const [showVoiceModal, setShowVoiceModal] = useState(false);

  const handleVoiceOnboarding = () => {
    setShowVoiceModal(true);
  };

  const handleCloseVoiceModal = () => {
    setShowVoiceModal(false);
  };

  return (
    <>
      <div className={`${styles.container} ${className || ""}`}>
        <h3 className={styles.title}>
          {t("How would you like to add your ELD provider?")}
        </h3>
        
        <div className={styles.optionsGrid}>
          {/* Option 1: Search */}
          <div className={styles.optionCard}>
            <div className={styles.optionHeader}>
              <SearchIcon fill="#3b82f6" className={styles.optionIcon} />
              <h4 className={styles.optionTitle}>{t("Search for Provider")}</h4>
            </div>
            <p className={styles.optionDescription}>
              {t("Find your ELD provider by name or company")}
            </p>
            <div className={styles.searchWrapper}>
              <Input
                label=""
                placeholder={t("Type provider name...")}
                value={query}
                onChange={(e: any) => setQuery(e.target.value)}
                icon={query ? <XIcon /> : <SearchIcon />}
                onIconClick={() => setQuery("")}
                size="medium"
              />
            </div>
          </div>

          {/* Option 2: Voice Onboarding */}
          <div className={styles.optionCard}>
            <div className={styles.optionHeader}>
              <PhoneIcon fill="#10b981" className={styles.optionIcon} />
              <h4 className={styles.optionTitle}>{t("Get Help via Call")}</h4>
            </div>
            <p className={styles.optionDescription}>
              {t("Let our experts guide you through the setup process")}
            </p>
            <button
              className={styles.callButton}
              onClick={handleVoiceOnboarding}
              data-testid="voice-onboarding-option"
            >
              <PhoneIcon fill="#fff" className={styles.buttonIcon} />
              {t("Start Guided Call")}
            </button>
          </div>

          {/* Option 3: Browse Popular */}
          <div className={styles.optionCard}>
            <div className={styles.optionHeader}>
              <div className={styles.gridIcon}>
                <div className={styles.gridDot}></div>
                <div className={styles.gridDot}></div>
                <div className={styles.gridDot}></div>
                <div className={styles.gridDot}></div>
              </div>
              <h4 className={styles.optionTitle}>{t("Browse Popular Providers")}</h4>
            </div>
            <p className={styles.optionDescription}>
              {t("Choose from our list of commonly used ELD providers")}
            </p>
            <button
              className={styles.browseButton}
              onClick={onBrowseProviders}
              data-testid="browse-providers-option"
            >
              {t("View All Providers")}
            </button>
          </div>
        </div>
      </div>

      <EldVoiceOnboardingModal 
        show={showVoiceModal} 
        onClose={handleCloseVoiceModal} 
      />
    </>
  );
};

export default ProviderSelectionOptions;
