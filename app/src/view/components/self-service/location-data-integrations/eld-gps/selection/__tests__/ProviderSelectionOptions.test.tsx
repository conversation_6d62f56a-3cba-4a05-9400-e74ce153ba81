import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";

import ProviderSelectionOptions from "../ProviderSelectionOptions";

// Mock the voice onboarding modal
jest.mock("../voice-onboarding/EldVoiceOnboardingModal", () => {
  return function MockEldVoiceOnboardingModal({ show, onClose }: any) {
    return show ? (
      <div data-testid="voice-onboarding-modal">
        <button onClick={onClose}>Close Modal</button>
      </div>
    ) : null;
  };
});

// Create a mock store
const createMockStore = () => {
  return configureStore({
    reducer: {
      // Add minimal reducer for testing
      test: (state = {}) => state,
    },
  });
};

describe("ProviderSelectionOptions", () => {
  const defaultProps = {
    query: "",
    setQuery: jest.fn(),
    onBrowseProviders: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders all three options correctly", () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <ProviderSelectionOptions {...defaultProps} />
      </Provider>
    );

    // Check that all three options are present
    expect(screen.getByText("Search for Provider")).toBeInTheDocument();
    expect(screen.getByText("Get Help via Call")).toBeInTheDocument();
    expect(screen.getByText("Browse Popular Providers")).toBeInTheDocument();
  });

  it("handles search input correctly", () => {
    const mockSetQuery = jest.fn();
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <ProviderSelectionOptions 
          {...defaultProps} 
          setQuery={mockSetQuery}
        />
      </Provider>
    );

    const searchInput = screen.getByPlaceholderText("Type provider name...");
    fireEvent.change(searchInput, { target: { value: "test provider" } });

    expect(mockSetQuery).toHaveBeenCalledWith("test provider");
  });

  it("opens voice onboarding modal when call button is clicked", () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <ProviderSelectionOptions {...defaultProps} />
      </Provider>
    );

    const callButton = screen.getByText("Start Guided Call");
    fireEvent.click(callButton);

    expect(screen.getByTestId("voice-onboarding-modal")).toBeInTheDocument();
  });

  it("calls onBrowseProviders when browse button is clicked", () => {
    const mockOnBrowseProviders = jest.fn();
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <ProviderSelectionOptions 
          {...defaultProps} 
          onBrowseProviders={mockOnBrowseProviders}
        />
      </Provider>
    );

    const browseButton = screen.getByText("View All Providers");
    fireEvent.click(browseButton);

    expect(mockOnBrowseProviders).toHaveBeenCalled();
  });
});
