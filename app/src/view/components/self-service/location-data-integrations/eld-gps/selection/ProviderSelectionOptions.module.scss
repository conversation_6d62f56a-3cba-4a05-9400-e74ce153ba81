.container {
  margin-bottom: 32px;
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24px;
  text-align: center;
}

.optionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.optionCard {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.2s ease-in-out;
  position: relative;
  
  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-2px);
  }
}

.optionHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.optionIcon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.gridIcon {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3px;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.gridDot {
  background-color: #8b5cf6;
  border-radius: 2px;
}

.optionTitle {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.optionDescription {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 20px;
  line-height: 1.5;
}

.searchWrapper {
  width: 100%;
  
  input {
    width: 100%;
  }
}

.callButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 12px 16px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: #059669;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
  }
}

.browseButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 12px 16px;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: #7c3aed;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);
  }
}

.buttonIcon {
  width: 16px;
  height: 16px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .optionsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .optionCard {
    padding: 20px;
  }
  
  .title {
    font-size: 18px;
    margin-bottom: 20px;
  }
}

/* Tablet responsiveness */
@media (max-width: 1024px) and (min-width: 769px) {
  .optionsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Focus states for accessibility */
.optionCard:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Animation for search input focus */
.searchWrapper input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
