import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import Card from "view/components/base/card/Card";
import StatusTag from "view/components/base/status-indicators/StatusTag";

import { carrierTrackingIntegrationsRoutes } from "router/carrier/CarrierTrackingIntegrationsRouter";

import { useAppSelector } from "state/hooks";
import { UsersState } from "state/modules/Users";

import CarrierSurveyApi, { SurveyStatusResponse } from "api/shipper/CarrierSurveyApi";

import styles from "./OnboardingActionItems.module.scss";

const OnboardingActionItems: React.FC = () => {
  const { t } = useTranslation();
  const history = useHistory();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [surveyStatus, setSurveyStatus] = useState<SurveyStatusResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/
  useEffect(() => {
    const fetchSurveyStatus = async () => {
      if (!carrierId) return;

      try {
        setIsLoading(true);
        setError(null);
        const response = await CarrierSurveyApi.getSurveyStatus(carrierId);
        setSurveyStatus(response);
      } catch (err) {
        console.error("Error fetching survey status:", err);
        setError("Failed to load onboarding status");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSurveyStatus();
  }, [carrierId]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /**
   * Redirect to ELD GPS integrations page for ELD onboarding
   */
  const redirectToEldOnboarding = () => {
    history.push(carrierTrackingIntegrationsRoutes.eldGps);
  };

  /**
   * Redirect to TMS onboarding page (placeholder for now)
   */
  const redirectToTmsOnboarding = () => {
    // TODO: Implement TMS onboarding page navigation
    console.log("TMS onboarding page will be built soon");
  };

  /**
   * Get status tag variant based on status
   */
  const getStatusVariant = (status: string) => {
    switch (status) {
      case "completed":
        return "ok";
      case "in_progress":
        return "alert";
      case "pending":
      default:
        return "error";
    }
  };

  /**
   * Get status display text
   */
  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return t("Completed");
      case "in_progress":
        return t("In Progress");
      case "pending":
      default:
        return t("Pending");
    }
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/



  if (isLoading) {
    return (
      <Card>
        <div className={styles.container}>
          <div className={styles.header}>
            <h3 className={styles.title}>{t("Action Items")}</h3>
          </div>
          <div className={styles.loader}>
            <Spinner isLoading size="medium" />
          </div>
        </div>
      </Card>
    );
  }

  if (error || !surveyStatus) {
    return (
      <Card>
        <div className={styles.container}>
          <div className={styles.header}>
            <h3 className={styles.title}>{t("Action Items")}</h3>
          </div>
          <div className={styles.error}>
            <p>{error || t("Unable to load onboarding status")}</p>
          </div>
        </div>
      </Card>
    );
  }

  // Don't show the component if there are no pending action items
  const hasActionItems =
    surveyStatus.eld_onboarding_status === "pending" ||
    surveyStatus.tms_onboarding_status === "pending";



  if (!hasActionItems) {
    return null;
  }

  return (
    <Card>
      <div className={styles.container}>
        <div className={styles.header}>
          <h3 className={styles.title}>{t("Action Items")}</h3>
          <p className={styles.subtitle}>
            {t("Complete these items to finish your onboarding")}
          </p>
        </div>

        <div className={styles.actionItems}>
          {surveyStatus.eld_onboarding_status === "pending" && (
            <div 
              className={styles.actionItem}
              onClick={redirectToEldOnboarding}
              data-test-id="eld-onboarding-action-item"
            >
              <div className={styles.actionItemContent}>
                <div className={styles.actionItemInfo}>
                  <h4 className={styles.actionItemTitle}>
                    {t("ELD Onboarding")}
                  </h4>
                  <p className={styles.actionItemDescription}>
                    {t("Set up your ELD provider for location tracking")}
                  </p>
                  {surveyStatus.eld_provider_names && surveyStatus.eld_provider_names.length > 0 && (
                    <p className={styles.providerInfo}>
                      {t("Provider")}: {surveyStatus.eld_provider_names.join(", ")}
                    </p>
                  )}
                </div>
                <div className={styles.actionItemStatus}>
                  <StatusTag
                    variant={getStatusVariant(surveyStatus.eld_onboarding_status)}
                    label={getStatusText(surveyStatus.eld_onboarding_status)}
                  />
                </div>
              </div>
            </div>
          )}

          {surveyStatus.tms_onboarding_status === "pending" && (
            <div 
              className={styles.actionItem}
              onClick={redirectToTmsOnboarding}
              data-test-id="tms-onboarding-action-item"
            >
              <div className={styles.actionItemContent}>
                <div className={styles.actionItemInfo}>
                  <h4 className={styles.actionItemTitle}>
                    {t("TMS Onboarding")}
                  </h4>
                  <p className={styles.actionItemDescription}>
                    {t("Set up your TMS integration")}
                  </p>
                  {surveyStatus.tms_provider_names && surveyStatus.tms_provider_names.length > 0 && (
                    <p className={styles.providerInfo}>
                      {t("Provider")}: {surveyStatus.tms_provider_names.join(", ")}
                    </p>
                  )}
                </div>
                <div className={styles.actionItemStatus}>
                  <StatusTag
                    variant={getStatusVariant(surveyStatus.tms_onboarding_status)}
                    label={getStatusText(surveyStatus.tms_onboarding_status)}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default OnboardingActionItems;
