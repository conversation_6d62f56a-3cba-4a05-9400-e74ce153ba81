import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router";

import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Tooltip } from "@fourkites/elemental-tooltip";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import Card from "view/components/base/card/Card";
import StatusTag from "view/components/base/status-indicators/StatusTag";

import { carrierRoutes } from "router/carrier/CarrierRouter";
import { carrierTrackingIntegrationsRoutes } from "router/carrier/CarrierTrackingIntegrationsRouter";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { DirectLoadAssignmentState } from "state/modules/carrier/DirectLoadAssignment";
import { EldGpsIntegrationsState } from "state/modules/carrier/EldGpsIntegrations";
import { CarrierIndicatorsState } from "state/modules/carrier/CarrierIndicators";
import { UsersState } from "state/modules/Users";

import { getIconForMode } from "view/components/base/ModeUtils";

import IndicatorsOutline from "view/components/self-service/overview/indicators-outline/IndicatorsOutline";
import AssetsOutline from "view/components/self-service/overview/assets-outline/AssetsOutline";
import PendingTrackingIntegrations from "view/components/self-service/overview/pending-setups/PendingTrackingIntegrations";
import OnboardingActionItems from "view/components/self-service/overview/onboarding-action-items/OnboardingActionItems";
import ThreePlStaticInformatory from "view/components/self-service/threePl/ThreePlStaticInformatory";

import styles from "./CarrierOverview.module.scss";


const CarrierOverview: React.FC = () => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();
  const history = useHistory();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);
  const isSuperAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);
  const isCompanyAdmin = useAppSelector(UsersState.selectors.getIsCompanyAdmin);
  // User
  const companyName: string = useAppSelector(
    UsersState.selectors.getCompanyName
  );
  const companyType = useAppSelector(UsersState.selectors.getCompanyType);
  const directAssignmentInfo = useAppSelector(
    UsersState.selectors.getCompanyDirectAssignmentInfo
  );

  // Carrier indicators
  const carrierIndicators = useAppSelector(
    CarrierIndicatorsState.selectors.carrierIndicators()
  );
  const isLoadingCarrierIndicators = useAppSelector(
    CarrierIndicatorsState.selectors.isLoadingCarrierIndicators()
  );

  // ELD GPS Indicators
  const eldGpsIndicators = carrierIndicators.trackingIntegrations?.eldGps;
  const isLoadingIntegrations = useAppSelector(
    EldGpsIntegrationsState.selectors.isRetrieving()
  );

  // Asset assignment
  const directAssignmentLoads = useAppSelector(
    DirectLoadAssignmentState.selectors.directAssignmentLoads()
  );
  const isLoadingDirectAssignmentLoads = useAppSelector(
    DirectLoadAssignmentState.selectors.isRetrieving()
  );

  const hasEldIntegrations = eldGpsIndicators?.total > 0;
  const isCarrier = companyType?.includes("carrier");

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  //For Ocean we will not make separate call as ocean has TL scacs only
  useEffect(() => {
    const modesWithIndicators = [
      "ltl",
      "ftl",
      "parcel",
      //"ocean"
    ];
    for (let mode of modesWithIndicators) {
      dispatch(
        CarrierIndicatorsState.actions.getCarrierIndicators({
          carrierId,
          //@ts-ignore
          mode,
        })
      );
    }
  }, [carrierId]);

  /*
   * Fetches info about load assignment
   */
  useEffect(() => {
    // Only carriers have direct assignment
    if (!isCarrier) {
      return;
    }

    if(directAssignmentInfo.apiToken){
      dispatch(
        DirectLoadAssignmentState.actions.retrieveDirectAssignmentLoads({
          apiToken: directAssignmentInfo.apiToken,
        })
      );
    }
  }, [directAssignmentInfo, isCarrier]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /**
   * Redirect to customers page from Overview
   */
  const redirectToCustomersPage = () => {
    history.push(`${carrierRoutes.customers}`);
  };

  /**
   * Redirect to assets page from Overview
   */
  const redirectToAssetsPage = () => {
    history.push(`${carrierRoutes.fleet}`);
  };

  /**
   * Redirect to tracking integrations page from Overview
   */
  const redirectToTrackingPage = () => {
    if (isSuperAdmin || isCompanyAdmin)
      history.push(`${carrierRoutes.tracking}`);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const gpsIndicators = [
    {
      principalIndicator: {
        value: eldGpsIndicators.total,
        component: t("Total"),
      },
      otherIndicators: [
        {
          value: eldGpsIndicators?.active,
          component: <StatusTag variant="ok" label="Active" />,
        },
        {
          value: eldGpsIndicators?.error,
          component: <StatusTag variant="error" label="Error" />,
        },
        {
          value: eldGpsIndicators?.pending,
          component: <StatusTag variant="alert" label="Pending" />,
        },
      ],
    },
  ];

  const customers = carrierIndicators?.customers;
  const asACarrier = (companyType?.includes("3pl") ? " (as a Carrier)" : "");

  //const FtlIcon = getIconForMode("ftl");
  //const LtlIcon = getIconForMode("ltl");
  //const OceanIcon = getIconForMode("ocean");
  //const ParcelIcon = getIconForMode("parcel");
  const customersIndicators = [
    {
      principalIndicator: {
        value: customers?.total?.total,
        component: <label>{t("Total")}</label>,
      },
      otherIndicators: [
        {
          value: customers?.total?.connected,
          component: <label>{t("Connected")}</label>,
        },
        {
          value: customers?.total?.inProgress,
          component: <label>{t("In Progress")}</label>,
        },
        {
          value: customers?.total?.disconnected,
          component: <label>{t("Disconnected")}</label>,
        },
      ],
    },
    // NOTE: removing indicators by mode for carriers as part of SELF-2166
    /*
    {
      principalIndicator: {
        value: customers?.ftl?.total,
        component: (
          <Tooltip placement="bottom" text={t("FTL")}>
            <div className={styles.modeIndicatorContainer}>
              <FtlIcon size="20px" />
            </div>
          </Tooltip>
        ),
      },
      otherIndicators: [
        {
          value: customers?.ftl?.connected,
          component: <label>{t("Connected")}</label>,
        },
        {
          value: customers?.ftl?.inProgress,
          component: <label>{t("In Progress")}</label>,
        },
        {
          value: customers?.ftl?.disconnected,
          component: <label>{t("Disconnected")}</label>,
        },
      ],
    },
    {
      principalIndicator: {
        value: customers?.ltl?.total,
        component: (
          <Tooltip placement="bottom" text={t("LTL")}>
            <div className={styles.modeIndicatorContainer}>
              <LtlIcon size="20px" />
            </div>
          </Tooltip>
        ),
      },
      otherIndicators: [
        {
          value: customers?.ltl?.connected,
          component: <label>{t("Connected")}</label>,
        },
        {
          value: customers?.ltl?.inProgress,
          component: <label>{t("In Progress")}</label>,
        },
        {
          value: customers?.ltl?.disconnected,
          component: <label>{t("Disconnected")}</label>,
        },
      ],
    },
    {
      principalIndicator: {
        value: customers?.parcel?.total,
        component: (
          <Tooltip placement="bottom" text={t("Parcel")}>
            <div className={styles.modeIndicatorContainer}>
              <ParcelIcon size="20px" />
            </div>
          </Tooltip>
        ),
      },
      otherIndicators: [
        {
          value: customers?.parcel?.connected,
          component: <label>{t("Connected")}</label>,
        },
        {
          value: customers?.parcel?.inProgress,
          component: <label>{t("In Progress")}</label>,
        },
        {
          value: customers?.parcel?.disconnected,
          component: <label>{t("Disconnected")}</label>,
        },
      ],
    },
    {
      principalIndicator: {
        value: customers?.ocean?.total,
        component: (
          <Tooltip placement="bottom" text={t("Ocean")}>
            <div className={styles.modeIndicatorContainer}>
              <OceanIcon size="20px" />
            </div>
          </Tooltip>
        ),
      },
      otherIndicators: [
        {
          value: customers?.ocean?.connected,
          component: <label>{t("Connected")}</label>,
        },
        {
          value: customers?.ocean?.inProgress,
          component: <label>{t("In Progress")}</label>,
        },
        {
          value: customers?.ocean?.disconnected,
          component: <label>{t("Disconnected")}</label>,
        },
      ],
    },*/
  ];

  const isLoading =
    isLoadingDirectAssignmentLoads || isLoadingCarrierIndicators;
  const isLoadingEld = isLoadingIntegrations && !hasEldIntegrations;

  if (isLoading) {
    return (
      <div className={styles.container}>
        {companyType?.includes("3pl") && <ThreePlStaticInformatory />}
        <div className={styles.titleContainer}>
          <BreadcrumbsHeader titles={[t("Customers & Integrations")]} />
        </div>

        <div className={styles.loader}>
          <Spinner isLoading size="medium" />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {companyType?.includes("3pl") && !companyType?.includes("broker") && <ThreePlStaticInformatory />}

      {/* Action Items Section - Always show if there are pending items */}
      <div className={styles.titleContainer}>
        <BreadcrumbsHeader titles={[t("Action Items")]} />
      </div>
      <div className={styles.content}>
        <div className={styles.contentColumn}>
          <OnboardingActionItems />
        </div>
        <div className={styles.contentColumn} />
        <div className={styles.contentColumn} />
      </div>

      {!isLoadingEld && !hasEldIntegrations && (isSuperAdmin || isCompanyAdmin) && (
        <>
          <div className={styles.titleContainer}>
            <BreadcrumbsHeader titles={[t("Pending Setups")]} />
          </div>

          <div className={styles.content}>
            <div className={styles.contentColumn}>
              <PendingTrackingIntegrations />
            </div>

            <div className={styles.contentColumn} />
            <div className={styles.contentColumn} />
          </div>
        </>
      )}

      <div>
        <div className={styles.titleContainer}>
          <BreadcrumbsHeader titles={[t("Customers & Integrations" + asACarrier)]} />
        </div>

        <div className={styles.content}>
          <div
            className={styles.contentColumnBig}
            data-test-id="card-customers"
          >
            <Card onClick={redirectToCustomersPage}>
              <IndicatorsOutline
                title="Customers"
                indicatorRows={customersIndicators}
              />
            </Card>
          </div>

          <div
            className={styles.contentColumn}
            data-test-id="card-asset-assignment"
          >
            {isCarrier && (
              <Card>
                <AssetsOutline
                  companyName={companyName}
                  directAssignmentInfo={directAssignmentInfo}
                  assigned={directAssignmentLoads?.assigned_count || 0}
                  notAssigned={directAssignmentLoads?.unassigned_count || 0}
                />
              </Card>
            )}
          </div>
        </div>

        {isLoadingEld && (
          <div className={styles.container}>
            <div className={styles.loader}>
              <Spinner isLoading size="medium" />
            </div>
          </div>
        )}

        {hasEldIntegrations && !isLoadingEld && (
          <div className={styles.content}>
            <div className={styles.contentColumnBig}>
              <Card onClick={redirectToTrackingPage}>
                <IndicatorsOutline
                  title="GPS Status"
                  indicatorRows={gpsIndicators}
                />
              </Card>
            </div>

            <div className={styles.contentColumn} />
          </div>
        )}
      </div>
    </div>
  );
};

export default CarrierOverview;
