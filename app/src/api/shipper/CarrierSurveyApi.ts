import { serviceUrls } from "api/http/apiUtils";
import { post, get } from "../http/service";

export interface SurveyStatusResponse {
  carrier_permalink: string;
  eld_onboarding_status: "pending" | "completed" | "in_progress";
  tms_onboarding_status: "pending" | "completed" | "in_progress";
  eld_provider_names: string[];
  tms_provider_names: string[];
  survey_exists: boolean;
  message: string;
}

class CarrierSurveyApi {
  /*
   * Uploads a carrier survey file
   */
  uploadCarrierSurvey = async (shipperId: string, file: File) => {
    const url = `${serviceUrls.selfOnboarding}/survey/${shipperId}/carrier-surveys/upload`;

    const formData = new FormData();
    formData.append('file', file);

    const response = await post(url, formData, {
      // @ts-ignore
      "Content-Type": `multipart/form-data; boundary=${formData?._boundary}`,
    });

    return response;
  };

  /*
   * Gets the survey status for a carrier
   */
  getSurveyStatus = async (carrierPermalink: string) => {
    const url = `${serviceUrls.selfOnboarding}/survey/status/${carrierPermalink}`;
    const response = await get(url);
    return response.data as SurveyStatusResponse;
  };
}

export default new CarrierSurveyApi();